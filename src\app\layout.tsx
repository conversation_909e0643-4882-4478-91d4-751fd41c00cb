import type { Metada<PERSON> } from "next";
import { Geist, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Navbar from "uf/components/Navbar";
import Footer from "uf/components/Footer";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Utsav Footwear - Quality Shoes for Every Occasion",
  description: "Your trusted destination for quality, comfortable, and stylish footwear. Discover our collection of shoes for every occasion at Utsav Footwear.",
  keywords: "footwear, shoes, quality shoes, comfortable shoes, stylish shoes, Utsav Footwear",
  authors: [{ name: "Utsav Footwear" }],
  openGraph: {
    title: "Utsav Footwear - Quality Shoes for Every Occasion",
    description: "Your trusted destination for quality, comfortable, and stylish footwear.",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <div className="flex flex-col min-h-screen">
          <Navbar />
          <main className="flex-grow">
            {children}
          </main>
          <Footer />
        </div>
      </body>
    </html>
  );
}
