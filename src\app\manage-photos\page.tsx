'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { signOut } from 'firebase/auth';
import { auth } from 'uf/lib/firebase';
import { productService, Product } from 'uf/lib/productService';
import AuthGuard from 'uf/components/AuthGuard';
import ProductCard from 'uf/components/ProductCard';
import PhotoUploadModal from 'uf/components/PhotoUploadModal';

export default function ManagePhotos() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [uploading, setUploading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const productsData = await productService.getAllProducts();
      setProducts(productsData);
    } catch (error) {
      console.error('Error fetching products:', error);
      alert('Failed to load products');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await signOut(auth);
      router.push('/');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const handleUpload = async (data: { file: File; name: string; caption: string }) => {
    setUploading(true);
    try {
      await productService.addProduct({
        name: data.name,
        caption: data.caption
      }, data.file);

      // Refresh products list
      await fetchProducts();
      setIsModalOpen(false);
      alert('Photo uploaded successfully!');
    } catch (error) {
      console.error('Upload error:', error);
      alert('Failed to upload photo. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const handleEdit = (productId: string) => {
    const product = products.find(p => p.id === productId);
    if (product) {
      setEditingProduct(product);
      setIsModalOpen(true);
    }
  };

  const handleUpdate = async (data: { file: File; name: string; caption: string }) => {
    if (!editingProduct) return;

    setUploading(true);
    try {
      await productService.updateProduct(editingProduct.id!, {
        name: data.name,
        caption: data.caption
      }, data.file);

      // Refresh products list
      await fetchProducts();
      setIsModalOpen(false);
      setEditingProduct(null);
      alert('Photo updated successfully!');
    } catch (error) {
      console.error('Update error:', error);
      alert('Failed to update photo. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  const handleDelete = async (productId: string) => {
    if (!confirm('Are you sure you want to delete this photo? This action cannot be undone.')) {
      return;
    }

    try {
      await productService.deleteProduct(productId);

      // Refresh products list
      await fetchProducts();
      alert('Photo deleted successfully!');
    } catch (error) {
      console.error('Delete error:', error);
      alert('Failed to delete photo. Please try again.');
    }
  };

  const handleModalSubmit = (data: { file: File; name: string; caption: string }) => {
    if (editingProduct) {
      handleUpdate(data);
    } else {
      handleUpload(data);
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setEditingProduct(null);
  };

  return (
    <AuthGuard>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <header className="bg-white shadow">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Photo Management</h1>
                <p className="text-gray-600">Manage your product gallery</p>
              </div>
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setIsModalOpen(true)}
                  className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                >
                  Add Photo
                </button>
                <button
                  onClick={handleLogout}
                  className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
                >
                  Logout
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Content */}
        <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          {loading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading products...</p>
            </div>
          ) : products.length === 0 ? (
            <div className="text-center py-12">
              <div className="bg-gray-100 w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-4xl">📷</span>
              </div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">No Photos Yet</h3>
              <p className="text-gray-600 mb-6">Start building your product gallery by adding your first photo.</p>
              <button
                onClick={() => setIsModalOpen(true)}
                className="bg-blue-600 text-white px-6 py-3 rounded-md hover:bg-blue-700 transition-colors"
              >
                Add First Photo
              </button>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {products.map((product) => (
                <ProductCard
                  key={product.id}
                  id={product.id!}
                  imageUrl={product.imageUrl}
                  caption={product.caption}
                  name={product.name}
                  onEdit={handleEdit}
                  onDelete={handleDelete}
                  isAdmin={true}
                />
              ))}
            </div>
          )}
        </main>

        {/* Upload Modal */}
        <PhotoUploadModal
          isOpen={isModalOpen}
          onClose={handleModalClose}
          onSubmit={handleModalSubmit}
          editData={editingProduct ? {
            id: editingProduct.id!,
            name: editingProduct.name,
            caption: editingProduct.caption,
            imageUrl: editingProduct.imageUrl
          } : null}
        />

        {/* Loading Overlay */}
        {uploading && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-700">
                {editingProduct ? 'Updating photo...' : 'Uploading photo...'}
              </p>
            </div>
          </div>
        )}
      </div>
    </AuthGuard>
  );
}
