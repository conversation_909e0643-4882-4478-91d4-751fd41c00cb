import { database, storage } from './firebase';
import { ref as dbRef, push, set, get, remove, update } from 'firebase/database';
import { ref as storageRef, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';

export interface Product {
  id?: string;
  name: string;
  caption: string;
  imageUrl: string;
  imagePath?: string;
  createdAt?: number;
  updatedAt?: number;
}

export class ProductService {
  private productsRef = dbRef(database, 'products');

  // Get all products
  async getAllProducts(): Promise<Product[]> {
    try {
      const snapshot = await get(this.productsRef);
      if (snapshot.exists()) {
        const data = snapshot.val();
        const products: Product[] = [];
        
        Object.keys(data).forEach(key => {
          products.push({
            id: key,
            ...data[key]
          });
        });

        // Sort by creation date (newest first)
        return products.sort((a, b) => {
          if (a.createdAt && b.createdAt) {
            return b.createdAt - a.createdAt;
          }
          return 0;
        });
      }
      return [];
    } catch (error) {
      console.error('Error fetching products:', error);
      throw new Error('Failed to fetch products');
    }
  }

  // Get a single product by ID
  async getProductById(id: string): Promise<Product | null> {
    try {
      const productRef = dbRef(database, `products/${id}`);
      const snapshot = await get(productRef);
      
      if (snapshot.exists()) {
        return {
          id,
          ...snapshot.val()
        };
      }
      return null;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw new Error('Failed to fetch product');
    }
  }

  // Add a new product
  async addProduct(productData: { name: string; caption: string }, imageFile: File): Promise<string> {
    try {
      // Upload image to Firebase Storage
      const timestamp = Date.now();
      const imagePath = `product-images/${productData.name}_${timestamp}_${imageFile.name}`;
      const imageRef = storageRef(storage, imagePath);
      
      await uploadBytes(imageRef, imageFile);
      const imageUrl = await getDownloadURL(imageRef);

      // Prepare product data
      const newProduct: Omit<Product, 'id'> = {
        ...productData,
        imageUrl,
        imagePath,
        createdAt: timestamp,
        updatedAt: timestamp
      };

      // Add to Realtime Database
      const newProductRef = push(this.productsRef);
      await set(newProductRef, newProduct);

      return newProductRef.key!;
    } catch (error) {
      console.error('Error adding product:', error);
      throw new Error('Failed to add product');
    }
  }

  // Update an existing product
  async updateProduct(id: string, productData: Partial<Product>, imageFile?: File): Promise<void> {
    try {
      const productRef = dbRef(database, `products/${id}`);
      const currentProduct = await this.getProductById(id);
      
      if (!currentProduct) {
        throw new Error('Product not found');
      }

      let imageUrl = currentProduct.imageUrl;
      let imagePath = currentProduct.imagePath;

      // If a new image is provided, upload it and delete the old one
      if (imageFile) {
        // Upload new image
        const timestamp = Date.now();
        const newImagePath = `product-images/${productData.name || currentProduct.name}_${timestamp}_${imageFile.name}`;
        const imageRef = storageRef(storage, newImagePath);
        
        await uploadBytes(imageRef, imageFile);
        imageUrl = await getDownloadURL(imageRef);

        // Delete old image if it exists
        if (currentProduct.imagePath) {
          try {
            const oldImageRef = storageRef(storage, currentProduct.imagePath);
            await deleteObject(oldImageRef);
          } catch (error) {
            console.error('Error deleting old image:', error);
          }
        }

        imagePath = newImagePath;
      }

      // Update product data
      const updatedProduct = {
        ...currentProduct,
        ...productData,
        imageUrl,
        imagePath,
        updatedAt: Date.now()
      };

      await update(productRef, updatedProduct);
    } catch (error) {
      console.error('Error updating product:', error);
      throw new Error('Failed to update product');
    }
  }

  // Delete a product
  async deleteProduct(id: string): Promise<void> {
    try {
      const product = await this.getProductById(id);
      
      if (!product) {
        throw new Error('Product not found');
      }

      // Delete image from Storage
      if (product.imagePath) {
        try {
          const imageRef = storageRef(storage, product.imagePath);
          await deleteObject(imageRef);
        } catch (error) {
          console.error('Error deleting image from storage:', error);
        }
      }

      // Delete product from Realtime Database
      const productRef = dbRef(database, `products/${id}`);
      await remove(productRef);
    } catch (error) {
      console.error('Error deleting product:', error);
      throw new Error('Failed to delete product');
    }
  }

  // Search products by name or caption
  async searchProducts(query: string): Promise<Product[]> {
    try {
      const allProducts = await this.getAllProducts();
      const searchTerm = query.toLowerCase();
      
      return allProducts.filter(product => 
        product.name.toLowerCase().includes(searchTerm) ||
        product.caption.toLowerCase().includes(searchTerm)
      );
    } catch (error) {
      console.error('Error searching products:', error);
      throw new Error('Failed to search products');
    }
  }
}

// Export a singleton instance
export const productService = new ProductService();
