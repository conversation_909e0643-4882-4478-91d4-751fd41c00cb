# Utsav Footwear Website

A modern e-commerce website for Utsav Footwear built with Next.js, TypeScript, Tailwind CSS, and Firebase.

## Features

- **SSR-first architecture** with Next.js App Router
- **Responsive design** with Tailwind CSS
- **Firebase integration** for authentication, database, and storage
- **Admin dashboard** for photo management
- **Contact form** with EmailJS integration
- **TypeScript** for type safety
- **Modern UI/UX** with smooth animations

## Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS 4
- **Backend**: Firebase (Auth, Firestore, Storage)
- **Email**: EmailJS
- **Deployment**: Firebase Hosting (planned)

## Pages

- `/` - Home page with store introduction
- `/about` - Founder story and store locations
- `/products` - Product gallery from Firebase
- `/contact` - Contact form using EmailJS
- `/manage-photos/login` - Admin login
- `/manage-photos` - Admin dashboard for photo management

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Firebase project
- EmailJS account (for contact form)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd utsavfootwear
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

4. Configure Firebase:
   - Create a Firebase project at [Firebase Console](https://console.firebase.google.com)
   - Enable Authentication, Firestore, and Storage
   - Copy your Firebase config to `.env.local`

5. Configure EmailJS:
   - Create an account at [EmailJS](https://www.emailjs.com)
   - Set up a service and template
   - Add your EmailJS credentials to `.env.local`

6. Run the development server:
```bash
npm run dev
```

7. Open [http://localhost:3000](http://localhost:3000) in your browser

## Firebase Setup

### Authentication
- Enable Email/Password authentication
- Create an admin user for photo management

### Firestore Database
- Create a `products` collection
- Set up security rules (see Firebase documentation)

### Storage
- Create a `products` folder for images
- Configure storage rules for admin access

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── about/             # About page
│   ├── contact/           # Contact page
│   ├── manage-photos/     # Admin section
│   ├── products/          # Products page
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # Reusable components
│   ├── AuthGuard.tsx      # Authentication guard
│   ├── Footer.tsx         # Site footer
│   ├── Navbar.tsx         # Navigation bar
│   ├── PhotoUploadModal.tsx # Photo upload modal
│   └── ProductCard.tsx    # Product display card
└── lib/
    └── firebase.ts        # Firebase configuration
```

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Import Alias

The project uses `uf/*` as an import alias for the `src/` directory:

```typescript
import Navbar from 'uf/components/Navbar';
import { db } from 'uf/lib/firebase';
```

## Deployment

The project is configured for Firebase Hosting deployment. See Firebase documentation for deployment instructions.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is private and proprietary to Utsav Footwear.
