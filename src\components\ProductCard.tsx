import Image from 'next/image';

interface ProductCardProps {
  id: string;
  imageUrl: string;
  caption: string;
  name?: string;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  isAdmin?: boolean;
}

export default function ProductCard({
  id,
  imageUrl,
  caption,
  name,
  onEdit,
  onDelete,
  isAdmin = false
}: ProductCardProps) {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
      {/* Image Container */}
      <div className="relative aspect-square">
        <Image
          src={imageUrl}
          alt={caption}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      </div>

      {/* Content */}
      <div className="p-4">
        {name && (
          <h3 className="font-semibold text-gray-900 mb-2 text-lg">{name}</h3>
        )}
        <p className="text-gray-700 text-sm leading-relaxed">{caption}</p>
        
        {/* Admin Controls */}
        {isAdmin && (onEdit || onDelete) && (
          <div className="mt-4 flex gap-2">
            {onEdit && (
              <button
                onClick={() => onEdit(id)}
                className="px-3 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600 transition-colors"
              >
                Edit
              </button>
            )}
            {onDelete && (
              <button
                onClick={() => onDelete(id)}
                className="px-3 py-1 bg-red-500 text-white text-xs rounded hover:bg-red-600 transition-colors"
              >
                Delete
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
